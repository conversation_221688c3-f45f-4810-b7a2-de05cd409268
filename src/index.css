@import "tailwindcss";
@import "tailwindcss-animated";
@import "remixicon/fonts/remixicon.css";
@import "./style/themes.css";

@plugin "daisyui" {
  themes:
    [ "light",
    "dark"];
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  html,
  body {
    font-family: "Noto Sans Thai", sans-serif;
  }

  /* Typography Styles */
  h1 {
    font-size: 28px;
    line-height: 36px;
    font-weight: 600;
  }

  h2 {
    font-size: 24px;
    line-height: 30px;
    font-weight: 600;
  }

  h3 {
    font-size: 20px;
    line-height: 26px;
    font-weight: 600;
  }

  h4 {
    font-size: 18px;
    line-height: 24px;
    font-weight: 600;
  }

  h5 {
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
  }

  h6,
  .text-h6 {
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
  }

  p,
  .text-body {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
  }

  .text-body-sm {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
  }

  .text-body-xs {
    font-size: 12px;
    line-height: 18px;
    font-weight: 400;
  }

  .text-label-sm {
    font-size: 13px;
    line-height: 20px;
    font-weight: 600;
  }

  .text-label-xs {
    font-size: 11px;
    line-height: 16px;
    font-weight: 600;
  }

  /* Custom Select Dropdown Styles */
  .select option {
    background-color: var(--color-base-100);
    color: var(--color-base-content);
    padding: 8px 12px;
    font-size: 14px;
    line-height: 20px;
  }

  .select option:hover,
  .select option:focus {
    background-color: var(--color-primary-content);
    color: var(--color-base-content);
  }

  .select option:checked {
    background-color: var(--color-primary);
    color: var(--color-base-100);
    font-weight: 600;
  }

  .select option:disabled {
    background-color: var(--color-base-200);
    color: var(--color-neutral);
    opacity: 0.6;
  }
}

html,
body,
#root {
  height: 100%;
}

.btn {
  border-radius: 0.8rem;
}
