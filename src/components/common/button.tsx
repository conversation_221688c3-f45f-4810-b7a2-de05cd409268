import { cn } from "@utils/cn";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  variant?: "primary" | "secondary" | "accent" | "outline" | "ghost" | "link" | "icon" | "select";
}

const BASE_CLASSES = "btn shadow-xs hover:shadow-none h-9";
const VARIANT_CLASSES: Record<NonNullable<ButtonProps["variant"]>, string> = {
  accent: "border-none btn-accent text-base-content hover:bg-accent/60",
  ghost:
    "border-none bg-transparent hover:bg-transparent hover:border-none boreder-none shadow-none",
  icon: "btn-icon border-none h-7 w-7 !rounded-lg p-0",
  link: "btn-link hover:text-primary-content shadow-none",
  outline:
    "btn-outline border hover:bg-secondary-content hover:border-secondary-content shadow-none",
  primary: "border-none btn-primary text-base-100 hover:bg-primary/70",
  secondary: "border-none btn-secondary text-base-100 hover:bg-secondary/70",
  select:
    "flex w-full appearance-none items-center justify-between rounded-lg bg-transparent shadow-none border-none px-4 focus:outline-none focus:ring-2 focus:ring-primary",
};

export const Button = ({ children, className, variant = "primary", ...props }: ButtonProps) => {
  return (
    <button
      type="button"
      className={cn(BASE_CLASSES, VARIANT_CLASSES[variant], className)}
      {...props}
    >
      {children}
    </button>
  );
};
