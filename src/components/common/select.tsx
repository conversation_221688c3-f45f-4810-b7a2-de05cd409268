import { CaretDownIcon } from "@phosphor-icons/react";
import { cn } from "@utils/cn";
import { type ReactNode, useCallback, useRef, useState } from "react";
import { Input } from "./input";

interface SelectOption {
  value: string;
  label: ReactNode;
}

interface SelectProps
  extends Omit<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    "size" | "value" | "onChange"
  > {
  className?: string;
  options?: SelectOption[];
  placeholder?: string;
  variant?: "default" | "popup";
  size?: "sm" | "md" | "lg";
  value?: string;
  onChange?: (value: string) => void;
}

const SIZE_CLASSES: Record<NonNullable<SelectProps["size"]>, string> = {
  lg: "h-10 text-sm",
  md: "h-9 text-body-xs",
  sm: "h-8 text-xs",
};

export const Select = ({
  className,
  options = [
    { label: "Yes", value: "Yes" },
    { label: "No", value: "No" },
  ],
  placeholder = "Empty",
  variant = "default",
  size = "md",
  value,
  onChange,
  id,
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = value
    ? options.find((option) => option.value === value)
    : null;

  const toggleDropdown = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const handleOptionSelect = useCallback(
    (optionValue: string) => {
      onChange?.(optionValue);
      setIsOpen(false);
    },
    [onChange]
  );

  return (
    <div ref={dropdownRef} className="relative w-full">
      <Input
        id={id}
        variant="transparent"
        className={cn(
          SIZE_CLASSES[size],
          !selectedOption?.label && "text-base-xs text-neutral",
          className
        )}
        isButton
        onBlur={() => {
          setTimeout(() => {
            if (!dropdownRef.current?.contains(document.activeElement)) {
              setIsOpen(false);
            }
          }, 0);
        }}
        onClick={toggleDropdown}
        value={selectedOption?.label}
        placeholder={placeholder}
        suffixIcon={
          variant === "default" && (
            <CaretDownIcon
              size={14}
              weight="bold"
              className={cn(
                "select-icon",
                { "rotate-180": isOpen },
                "transition-transform duration-200"
              )}
            />
          )
        }
        // {...props}
      />
      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 w-full rounded-lg border border-base-300 bg-base-100 p-2 shadow-lg">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleOptionSelect(option.value)}
              className={cn(
                "w-fit rounded-lg px-4 py-2 text-left hover:bg-accent-content",
                {
                  "bg-success-content hover:bg-success-content":
                    value === option.value,
                }
              )}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
