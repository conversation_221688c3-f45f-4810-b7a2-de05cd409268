import { ChannelBadge, type ChanneType } from "@components/badge/channelBadge";
import { OpportunityBadge, type OpportunityType } from "@components/badge/opportunityBadge";

export const _FOLLOW_UP_STATUS_OPTIONS = [
  {
    label: <div className="rounded-full bg-accent px-4 py-1 text-label-xs">รอติดตาม</div>,
    value: "pending",
  },
  {
    label: <div className="rounded-full bg-secondary-content px-4 py-1 text-label-xs">ติดต่อแล้ว</div>,
    value: "contacted",
  },
  {
    label: <div className="rounded-full bg-primary-content px-4 py-1 text-label-xs">สนใจ</div>,
    value: "interested",
  },
  {
    label: <div className="rounded-full bg-base-200 px-4 py-1 text-label-xs">ไม่สนใจ</div>,
    value: "not_interested",
  },
];

export const _OPPORTUNITY_OPTIONS = (["hot", "warm", "cold"] as OpportunityType[]).map((type) => ({
  label: <OpportunityBadge type={type} />,
  value: type,
}));

export const _CONTACT_CHANNEL_OPTIONS = (
  ["line", "facebook", "instagram", "tiktok"] as ChanneType[]
).map((type) => ({
  label: <ChannelBadge type={type} />,
  value: type,
}));
