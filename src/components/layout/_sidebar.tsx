import { ROUTE_PATH } from "@enums/route-path";
import {
  CheckSquareOffsetIcon,
  GearSixIcon,
  HouseIcon,
  SidebarIcon,
  UsersThreeIcon,
} from "@phosphor-icons/react";
import { Link } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import { useState } from "react";

const MENU_ITEMS = [
  {
    activeIcon: <HouseIcon size={24} weight="fill" />,
    icon: <HouseIcon size={24} />,
    label: "Home",
    path: ROUTE_PATH.HOME,
  },
  {
    activeIcon: <CheckSquareOffsetIcon size={24} weight="fill" />,
    icon: <CheckSquareOffsetIcon size={24} />,
    label: "Follow Up",
    path: ROUTE_PATH.FOLLOW_UP,
  },
  {
    activeIcon: <UsersThreeIcon size={24} weight="fill" />,
    icon: <UsersThreeIcon size={24} />,
    label: "All Leads",
    path: ROUTE_PATH.ALL_LEADS,
  },
  {
    activeIcon: <GearSixIcon size={24} weight="fill" />,
    icon: <GearSixIcon size={24} />,
    label: "Settings",
    path: ROUTE_PATH.COLORS,
  },
];

export const Sidebar = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={cn("sticky top-0 h-full", isExpanded ? "p-2" : "py-2")}>
      <div
        className={cn(
          "menu-vertical h-full items-end gap-2 border bg-base-100 p-2 shadow-sm",
          isExpanded ? "w-40 rounded-lg border-base-200" : "w-16 rounded-r-lg border-base-100",
          "transition-[width] duration-300",
        )}
      >
        <button
          type="button"
          onClick={toggleExpanded}
          className={cn(
            "mb-8 flex h-fit cursor-pointer items-center justify-center rounded-lg p-3",
            "hover:bg-primary-content/30 [&.active]:bg-base-200",
            isExpanded && "active",
          )}
        >
          <SidebarIcon size={24} />
        </button>

        {MENU_ITEMS.map(({ activeIcon, icon, label, path }) => (
          <Link
            key={path}
            to={path as string}
            className={cn(
              "flex w-full items-center rounded-lg p-3 text-xl hover:bg-primary-content/30",
              "[&.active]:bg-primary [&.active]:font-bold [&.active]:text-base-100",
              isExpanded ? "gap-3" : "justify-start",
            )}
          >
            <i className="flex-shrink-0">
              <span className="[.active_&]:hidden">{icon}</span>
              <span className="hidden [.active_&]:inline">{activeIcon}</span>
            </i>

            <span
              className={cn(
                "whitespace-nowrap font-medium text-sm transition-all duration-100 ease-in-out",
                isExpanded
                  ? "max-w-none translate-x-0 scale-100 opacity-100"
                  : "max-w-0 translate-x-4 scale-95 overflow-hidden opacity-0",
              )}
            >
              {label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};
