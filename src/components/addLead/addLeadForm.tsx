import {
  _CONTACT_CHANNEL_OPTIONS,
  _FOLLOW_UP_STATUS_OPTIONS,
  _OPPORTUNITY_OPTIONS,
} from "@components/addLead/mockOptions";
import { Button } from "@components/common/button";
import { Input } from "@components/common/input";
import { Select } from "@components/common/select";
import { Textarea } from "@components/common/textarea";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const [followUpStatus, setFollowUpStatus] = useState<string | null>(null);
  const [opportunity, setOpportunity] = useState<string | null>(null);
  const [contactChannel, setContactChannel] = useState<string | null>(null);
  const [servicesOfInterest, setServicesOfInterest] = useState<string | null>(
    null
  );

  return (
    <form className="flex h-full flex-col gap-6 overflow-hidden p-1">
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          {/* ชื่อ - Required field */}
          <div className="col-span-2 grid grid-cols-3">
            <label htmlFor="name" className="flex gap-1">
              <span className="text-h6">{t("addLead.name")}</span>
              <span className="text-error text-h6">*</span>
            </label>
            <Input
              id="name"
              type="text"
              placeholder="Name"
              className="col-span-2 flex-1"
              variant="transparent"
            />
          </div>
          {/* โอกาส */}
          <div className="grid grid-cols-2">
            <label htmlFor="opportunity" className="text-h6">
              {t("addLead.opportunity")}
            </label>
            <Select
              id="opportunity"
              options={_OPPORTUNITY_OPTIONS}
              size="sm"
              variant="popup"
              value={opportunity ?? "-"}
              onChange={setOpportunity}
            />
          </div>
          {/* สถานะติดตาม */}
          <div className="col-span-2 grid grid-cols-3">
            <label htmlFor="followUpStatus" className="text-h6">
              {t("addLead.followUpStatus")}
            </label>
            <Select
              id="followUpStatus"
              options={_FOLLOW_UP_STATUS_OPTIONS}
              size="sm"
              variant="popup"
              value={followUpStatus ?? "-"}
              onChange={setFollowUpStatus}
              className="col-span-2 flex-1"
            />
          </div>
          {/* ช่องทางติดต่อ */}
          <div className="grid grid-cols-2">
            <label htmlFor="contactChannel" className="flex items-center gap-1">
              <span className="text-h6">{t("addLead.contactChannel")}</span>
              <span className="text-error text-h6">*</span>
            </label>
            <Select
              id="contactChannel"
              options={_CONTACT_CHANNEL_OPTIONS}
              size="sm"
              variant="popup"
              value={contactChannel ?? "-"}
              onChange={setContactChannel}
            />
          </div>
          {/* บริการที่สนใจ */}
          <div className="col-span-2 grid grid-cols-3">
            <label htmlFor="servicesOfInterest" className="text-h6">
              {t("addLead.servicesOfInterest")}
            </label>
            <Select
              id="servicesOfInterest"
              options={_FOLLOW_UP_STATUS_OPTIONS}
              size="sm"
              variant="popup"
              value={servicesOfInterest ?? "-"}
              onChange={setServicesOfInterest}
              className="!col-span-2 w-full"
            />
          </div>
          {/* วันที่เริ่ม */}
          <div className="grid grid-cols-2">
            <label htmlFor="startDate" className="text-h6">
              {t("addLead.startDate")}
            </label>
            <div className="relative">
              <Input
                id="startDate"
                type="date"
                defaultValue={today}
                className="w-32"
              />
            </div>
          </div>
          {/* ติดต่อ */}
          <div className="col-span-2 grid grid-cols-3">
            <label htmlFor="contactInfo" className="text-h6">
              {t("addLead.contactInfo")}
            </label>
            <Input
              id="contactInfo"
              type="text"
              placeholder="Contact"
              className="col-span-2 flex-1"
              variant="transparent"
            />
          </div>
          {/* วันที่ติดตาม */}
          <div className="grid grid-cols-2">
            <label htmlFor="followUpDate" className="text-h6">
              {t("addLead.followUpDate")}
            </label>
            <div className="relative">
              <Input id="followUpDate" value={"-"} className="w-32" disabled />
            </div>
          </div>
        </div>
      </div>

      {/* บันทึก */}
      <div className="flex min-h-0 flex-1 flex-col gap-2">
        <label htmlFor="note" className="text-h6">
          {t("addLead.note")}
        </label>
        <Textarea
          id="note"
          placeholder="เพิ่มบันทึกเพิ่มเติม..."
          className="min-h-24 w-full flex-1 resize-none"
        />
      </div>

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28">
          แบบร่าง
        </Button>
        <Button className="w-28">เพิ่ม</Button>
      </div>
    </form>
  );
};
