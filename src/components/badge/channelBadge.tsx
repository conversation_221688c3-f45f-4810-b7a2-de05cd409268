import { cn } from "@utils/cn";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

interface ChannelBadgeConfig {
  type: ChanneType;
  icon: string;
  iconAlt: string;
  containerClasses: string;
}

const CHANNEL_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "text-blue-500",
    icon: "ri-facebook-circle-fill",
    iconAlt: "Facebook",
    type: "facebook",
  },
  instagram: {
    containerClasses: "text-pink-500",
    icon: "ri-instagram-fill",
    iconAlt: "Instagram",
    type: "instagram",
  },
  line: {
    containerClasses: "text-green-500",
    icon: "ri-line-fill",
    iconAlt: "Line",
    type: "line",
  },
  tiktok: {
    containerClasses: "text-black",
    icon: "ri-tiktok-fill",
    iconAlt: "Tiktok",
    type: "tiktok",
  },
};

const BASE_BADGE_CLASSES = "flex items-center aspect-square text-2xl";

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
}

export const ChannelBadge = ({ type, className }: ChannelBadgeProps) => {
  if (!type) {
    return null;
  }

  const config = CHANNEL_BADGE_CONFIG[type];

  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      <i className={cn(config.icon)} />
    </div>
  );
};
